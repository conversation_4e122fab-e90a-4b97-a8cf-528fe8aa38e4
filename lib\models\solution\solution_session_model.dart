// To parse this JSON data, do
//
//     final solutionSessionModel = solutionSessionModelFromJson(jsonString);

import 'dart:convert';

SolutionSessionModel solutionSessionModelFromJson(String str) =>
    SolutionSessionModel.fromJson(json.decode(str));

String solutionSessionModelToJson(SolutionSessionModel data) =>
    json.encode(data.toJson());

class SolutionSessionModel {
  String? sessionId;
  String? status;
  int? initialNodesUpdated;
  double? currentWeight;
  String? nextQuestion;
  String? nextNodeId;

  SolutionSessionModel({
    this.sessionId,
    this.status,
    this.initialNodesUpdated,
    this.currentWeight,
    this.nextQuestion,
    this.nextNodeId,
  });

  SolutionSessionModel copyWith({
    String? sessionId,
    String? status,
    int? initialNodesUpdated,
    double? currentWeight,
    String? nextQuestion,
    String? nextNodeId,
  }) =>
      SolutionSessionModel(
        sessionId: sessionId ?? this.sessionId,
        status: status ?? this.status,
        initialNodesUpdated: initialNodesUpdated ?? this.initialNodesUpdated,
        currentWeight: currentWeight ?? this.currentWeight,
        nextQuestion: nextQuestion ?? this.nextQuestion,
        nextNodeId: nextNodeId ?? this.nextNodeId,
      );

  factory SolutionSessionModel.fromJson(Map<String, dynamic> json) =>
      SolutionSessionModel(
        sessionId: json["session_id"],
        status: json["status"],
        initialNodesUpdated: json["initial_nodes_updated"],
        currentWeight: json["current_weight"]?.toDouble(),
        nextQuestion: json["next_question"],
        nextNodeId: json["next_node_id"],
      );

  Map<String, dynamic> toJson() => {
        "session_id": sessionId,
        "status": status,
        "initial_nodes_updated": initialNodesUpdated,
        "current_weight": currentWeight,
        "next_question": nextQuestion,
        "next_node_id": nextNodeId,
      };
}
