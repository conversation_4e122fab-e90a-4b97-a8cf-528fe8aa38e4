import 'package:dio/dio.dart';
import 'package:nsl/models/multimedia/file_upload_ocr_response.dart';
import '../config/environment.dart';
import '../services/dio_client.dart';
import '../utils/logger.dart';

class ChatApiService {
  final Dio _dio = DioClient().client;

  // Base URL
  late final String _baseUrl;

  // API endpoints
  late final String _generalApiUrl;
  late final String _internetApiUrl;
  late final String _nslApiUrl;
  late final String _manageConversationsUrl;
  late final String _getChatHistoryUrl;
  late final String _newConversationUrl;
  late final String _conversationUrl;
  late final String _solutionSessionsUrl;
  late final String _solutionMessageUrl;

  // Singleton instance
  static final ChatApiService _instance = ChatApiService._internal();

  // Factory constructor
  factory ChatApiService() => _instance;

  // Internal constructor
  ChatApiService._internal() {
    // Initialize base URL from environment
    _baseUrl = Environment.chatApiBaseUrl;
    Logger.info('Chat API base URL: $_baseUrl');

    // Initialize API endpoints
    _generalApiUrl = '$_baseUrl/ask/general';
    _internetApiUrl = '$_baseUrl/ask/internet';
    _nslApiUrl = '$_baseUrl/ask/nsl';
    _manageConversationsUrl = '$_baseUrl/manage_conversations';
    _getChatHistoryUrl = '$_baseUrl/get_chat_history';
    _newConversationUrl = 'http://10.26.1.52:8003/conversation/new';
    _conversationUrl = 'http://10.26.1.52:8003/conversation';
    _solutionSessionsUrl = 'http://10.26.1.52:8007/brd/sessions';
    _solutionMessageUrl = 'http://10.26.1.52:8007/brd/sessions';
  }

  // Create a new conversation
  Future<Map<String, dynamic>> createConversation(
      String userId, String title) async {
    try {
      Logger.info(
          'Creating new conversation for user $userId with title: $title');

      // Prepare request payload
      final payload = {'action': 'create', 'user_id': userId, 'title': title};

      // Make API call
      final response = await _dio.post(
        _manageConversationsUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Extract conversation ID from response
        final conversationId = response.data['conversation_id'];

        if (conversationId != null) {
          Logger.info('Created new conversation with ID: $conversationId');

          return {
            'success': true,
            'data': response.data,
            'conversation_id': conversationId.toString(),
          };
        } else {
          Logger.error(
              'Create conversation API did not return a conversation_id');
          return {
            'success': false,
            'message': 'No conversation ID returned',
          };
        }
      } else {
        Logger.error(
            'Create conversation API failed with status: ${response.statusCode}');
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating conversation: $e');
      return {
        'success': false,
        'message': 'Failed to create conversation: $e',
      };
    }
  }

  // Send a general question to the API
  Future<Map<String, dynamic>> sendGeneralQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending general question to API: $question');

      // Prepare request payload
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _generalApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('General API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending general question: $e');
      return {
        'success': false,
        'message': 'Failed to send question: $e',
      };
    }
  }

  // Send an internet question to the API
  Future<Map<String, dynamic>> sendInternetQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending internet question to API: $question');

      // Prepare request payload with conversation ID
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _internetApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Internet API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending internet question: $e');
      return {
        'success': false,
        'message': 'Failed to send internet question: $e',
      };
    }
  }

  // Send an NSL question to the API
  Future<Map<String, dynamic>> sendNslQuestion(
      String question, String conversationId, String userId) async {
    try {
      Logger.info('Sending NSL question to API: $question');

      // Prepare request payload with conversation ID
      final payload = {
        'question': question,
        'user_id': userId,
        'conversation_id': conversationId,
        'include_reasoning': true
      };

      // Make API call
      final response = await _dio.post(
        _nslApiUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('NSL API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending NSL question: $e');
      return {
        'success': false,
        'message': 'Failed to send NSL question: $e',
      };
    }
  }

  // Fetch chat history list
  Future<Map<String, dynamic>> fetchChatHistory(String userId) async {
    try {
      Logger.info('Fetching chat history for user: $userId');

      // Prepare request payload
      final payload = {'action': 'list', 'user_id': userId};

      // Make API call
      final response = await _dio.post(
        _manageConversationsUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Chat history API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error fetching chat history: $e');
      return {
        'success': false,
        'message': 'Failed to fetch chat history: $e',
      };
    }
  }

  // Fetch chat history for a specific conversation
  Future<Map<String, dynamic>> fetchConversationHistory(
      String conversationId, String userId) async {
    try {
      Logger.info('Fetching chat history for conversation: $conversationId');

      // Prepare request payload
      final payload = {'conversation_id': conversationId, 'user_id': userId};

      // Make API call
      final response = await _dio.post(
        _getChatHistoryUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Get chat history API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
          'conversation_id': conversationId,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
          'conversation_id': conversationId,
        };
      }
    } catch (e) {
      Logger.error('Error fetching conversation history: $e');
      return {
        'success': false,
        'message': 'Failed to fetch conversation history: $e',
        'conversation_id': conversationId,
      };
    }
  }

  // Create a new conversation session
  Future<Map<String, dynamic>> createNewConversationSession() async {
    try {
      Logger.info('Creating new conversation session');

      // Make API call with empty body
      final response = await _dio.post(
        _newConversationUrl,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Create new conversation session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        // The response directly contains session_id and message
        return {
          'success': true,
          'session_id': response.data['session_id'],
          'message': response.data['message'],
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating new conversation session: $e');
      return {
        'success': false,
        'message': 'Failed to create new conversation session: $e',
      };
    }
  }

  // Send user input to conversation API
  Future<Map<String, dynamic>> sendConversationInput(
      String sessionId, String userInput,
      {FileUploadOcrResponse? fileData}) async {
    try {
      Logger.info('Sending user input to conversation API');

      // Prepare request payload
      final Map<String, dynamic> payload = {
        'session_id': sessionId,
        'user_input': userInput,
      };

      // Add file data if available
      // if (fileData != null) {
      //   payload['file_data'] = fileData;
      //   // Logger.info(
      //   //     'Including file data in conversation API request: ${fileData['fileName']}');
      // }

      // Make API call
      final response = await _dio.post(
        _conversationUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info('Conversation API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error sending user input to conversation API: $e');
      return {
        'success': false,
        'message': 'Failed to send user input to conversation API: $e',
      };
    }
  }

  // Create a new solution session for the first message
  Future<Map<String, dynamic>> createSolutionSession(
      String tenantId, String projectId, String initialInput) async {
    try {
      Logger.info('Creating new solution session for tenant: $tenantId');

      // Prepare request payload
      final Map<String, dynamic> payload = {
        'tenant_id': tenantId,
        'project_id': projectId,
        'initial_input': initialInput,
      };

      // Make API call
      final response = await _dio.post(
        _solutionSessionsUrl,
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Solution session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating solution session: $e');
      return {
        'success': false,
        'message': 'Failed to create solution session: $e',
      };
    }
  }

  Future<Map<String, dynamic>> createSolutionMessage(
      String sessionId, String userResponse, String nodeId) async {
    try {
      // Prepare request payload
      final Map<String, dynamic> payload = {
        'session_id': sessionId,
        'user_response': userResponse,
        'node_id': nodeId,
      };

      // Make API call
      final response = await _dio.post(
        "$_solutionMessageUrl/$sessionId/responses",
        data: payload,
        options: Options(
          headers: {
            'Content-Type': 'application/json',
          },
        ),
      );

      Logger.info(
          'Solution session API response status: ${response.statusCode}');

      if (response.statusCode == 200) {
        return {
          'success': true,
          'data': response.data,
        };
      } else {
        return {
          'success': false,
          'message': response.data['message'] ?? 'Unknown error',
        };
      }
    } catch (e) {
      Logger.error('Error creating solution session: $e');
      return {
        'success': false,
        'message': 'Failed to create solution session: $e',
      };
    }
  }
}
