// import 'package:flutter/material.dart';
// import 'package:nsl/providers/discovery_provider.dart';
// import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
// import 'package:nsl/services/discovery_service.dart';
// import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
// import 'package:provider/provider.dart';

// class DiscoveryChatScreen extends StatefulWidget {
//   const DiscoveryChatScreen({super.key});

//   @override
//   State<DiscoveryChatScreen> createState() => _DiscoveryChatScreenState();
// }

// class _DiscoveryChatScreenState extends State<DiscoveryChatScreen> {
//   DiscoveryService _apoService = DiscoveryService();
//   Stream<List>? _responseStream;
//   final List<String> _messages = [];
//   final TextEditingController _controller =
//       TextEditingController(text: "Hi, who are you?");

//   void _startStreaming() {
//     setState(() {
//       _messages.clear();
//       _responseStream = _apoService.getUsersStream(_controller.text);
//     });
//   }

//   @override
//   void initState() {
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     final discoveryProvider = Provider.of<DiscoveryProvider>(context);

//     return NSLKnowledgeLoaderWrapper(
//       isLoading: discoveryProvider.isLoading ||
//           discoveryProvider.isFileLoading ||
//           discoveryProvider.isSpeechLoading,
//       child: Column(
//         children: [
//           Expanded(
//             child: StreamBuilder<List>(
//               stream: _responseStream,
//               builder: (context, snapshot) {
//                 if (snapshot.hasData) {
//                   _messages.add(snapshot.data!.toString());
//                 }
//                 return ListView.builder(
//                   itemCount: _messages.length,
//                   itemBuilder: (context, index) => Text(_messages[index]),
//                 );
//               },
//             ),
//           ),
//           Padding(
//               padding: const EdgeInsets.all(8.0),
//               child: ChatField(
//                 controller: _controller,
//                 isGeneralLoading: discoveryProvider.isLoading,
//                 isFileLoading: discoveryProvider.isFileLoading,
//                 isSpeechLoading: discoveryProvider.isSpeechLoading,
//                 onSendMessage: () => _startStreaming(),
//                 onFileSelected: (p0, p1) {},
//                 onToggleRecording: () {},
//               )),
//         ],
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import 'package:nsl/providers/discovery_provider.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/chat_field.dart';
import 'package:nsl/widgets/common/nsl_knowledge_loader.dart';
import 'package:provider/provider.dart';
import 'package:typewritertext/typewritertext.dart';

class DiscoveryChatScreen extends StatefulWidget {
  const DiscoveryChatScreen({super.key});

  @override
  State<DiscoveryChatScreen> createState() => _DiscoveryChatScreenState();
}

class _DiscoveryChatScreenState extends State<DiscoveryChatScreen> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final discoveryProvider = Provider.of<DiscoveryProvider>(context);

    return Consumer<DiscoveryProvider>(
      builder: (context, value, child) {
        return NSLKnowledgeLoaderWrapper(
          isLoading: discoveryProvider.isLoading ||
              discoveryProvider.isFileLoading ||
              discoveryProvider.isSpeechLoading,
          child: Column(
            children: [
              Expanded(
                child: Text(
                  discoveryProvider.response,
                ),
              ),
              Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: ChatField(
                    controller: discoveryProvider.chatController,
                    isGeneralLoading: discoveryProvider.isLoading,
                    isFileLoading: discoveryProvider.isFileLoading,
                    isSpeechLoading: discoveryProvider.isSpeechLoading,
                    onSendMessage: () => discoveryProvider
                        .sendMessage(discoveryProvider.chatController.text),
                    onFileSelected: (p0, p1) {},
                    onToggleRecording: () {},
                  )),
            ],
          ),
        );
      },
    );
  }
}
