import 'dart:async';

import 'package:flutter/material.dart';
import 'package:nsl/services/discovery_service.dart';
import 'package:typewritertext/typewritertext.dart';

class DiscoveryProvider with ChangeNotifier {
  // bool isLoading = false;
  bool isFileLoading = false;
  bool isSpeechLoading = false;
  TextEditingController chatController =
      TextEditingController(text: "Hi, who are you?");

  final DiscoveryService _apiService = DiscoveryService();
  String _response = "";
  StreamSubscription<String>? _subscription;
  final controller = TypeWriterController(
    text: '',
    duration: const Duration(milliseconds: 50),
  );

  String get response => _response;

  void sendMessage(String message) {
    // Clear previous response
    _response = "";
    notifyListeners();

    // Cancel existing subscription
    _subscription?.cancel();

    _subscription = _apiService.sendMessage(message, []).listen((newChunk) {
      // Append new chunk to existing response
      _response += newChunk;
      notifyListeners();
    }, onError: (error) {
      _response = "Error: $error";
      notifyListeners();
    });
  }

  List<String> _messages = [];
  bool _isLoading = false;

  List<String> get messages => _messages;
  bool get isLoading => _isLoading;

  // Future<void> sendMessage(String message) async {
  //   _messages.clear();
  //   _isLoading = true;
  //   notifyListeners();

  //   final stream = _apiService.sendMessage(message);

  //   await for (final chunk in stream) {
  //     _messages.add(chunk);
  //     notifyListeners(); // 👈 this will refresh UI for each chunk
  //   }

  //   _isLoading = false;
  //   notifyListeners();
  // }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
