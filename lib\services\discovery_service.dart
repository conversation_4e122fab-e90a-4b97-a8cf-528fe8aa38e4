// import 'dart:async';
// import 'dart:convert';
// import 'package:http/http.dart' as http;

// class DiscoveryService {
//   static const String _baseUrl = "http://10.26.1.52:8445/chat";

//   Stream<String> sendMessage(String message, List<dynamic> history) async* {
//     final uri = Uri.parse(_baseUrl);
//     final headers = {
//       'Accept': 'application/octet-stream',
//       'Content-Type': 'application/json',
//     };
//     final body = jsonEncode({
//       'message': message,
//       'history': history,
//     });

//     final request = http.Request('POST', uri)
//       ..headers.addAll(headers)
//       ..body = body;

//     final client = http.Client();
//     final streamedResponse = await client.send(request);

//     StringBuffer buffer = StringBuffer();

//     await for (final chunk in streamedResponse.stream
//         .transform(utf8.decoder)
//         .transform(const LineSplitter())) {
//       if (chunk.isEmpty) continue;

//       try {
//         final body = chunk.substring(6);
//         final jsonData = jsonDecode(body);
//         if (jsonData['type'] == 'content' && jsonData['content'] != null) {
//           final content = jsonData['content'] as String;
//           buffer.write(content);
//           yield buffer.toString(); // Yield accumulated content
//         }
//       } catch (e) {
//         yield "Error parsing response: $e";
//       }
//     }
//     client.close();
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;

class DiscoveryService {
  static const String _baseUrl = "http://10.26.1.52:8445/chat";

  Stream<String> sendMessage(String message, List<dynamic> history) async* {
    final uri = Uri.parse(_baseUrl);
    final headers = {
      'Accept': 'application/octet-stream',
      'Content-Type': 'application/json',
    };
    final body = jsonEncode({
      'message': message,
      'history': history,
    });

    final request = http.Request('POST', uri)
      ..headers.addAll(headers)
      ..body = body;

    final client = http.Client();
    final streamedResponse = await client.send(request);

    await for (final chunk in streamedResponse.stream
        .transform(utf8.decoder)
        .transform(const LineSplitter())) {
      if (chunk.isEmpty) continue;

      try {
        final body = chunk.substring(6);
        final jsonData = jsonDecode(body);
        if (jsonData['type'] == 'content' && jsonData['content'] != null) {
          yield jsonData['content'] as String;
        }
      } catch (e) {
        yield "Error: $e";
      }
    }
    client.close();
  }

  final Dio _dio = Dio();

  // Stream<String> sendMessage(String message) {
  //   final controller = StreamController<String>();

  //   _dio
  //       .post<ResponseBody>(
  //     'http://10.26.1.52:8445/chat',
  //     data: jsonEncode({'message': message}),
  //     options: Options(
  //       responseType: ResponseType.stream,
  //       headers: {
  //         'Accept': 'application/octet-stream',
  //         'Content-Type': 'application/json',
  //       },
  //     ),
  //   )
  //       .then((response) {
  //     final stream = response.data!.stream;

  //     // Use a StringBuffer to collect partial UTF-8 characters safely
  //     final buffer = StringBuffer();
  //     final decoder = Utf8Decoder();

  //     stream.listen(
  //       (Uint8List chunk) {
  //         final decoded = decoder.convert(chunk);
  //         buffer.write(decoded);
  //         controller.add(decoded); // Emit every chunk as soon as it comes
  //       },
  //       onError: (e) {
  //         controller.addError('Stream error: $e');
  //         controller.close();
  //       },
  //       onDone: () {
  //         controller.close();
  //       },
  //       cancelOnError: true,
  //     );
  //   }).catchError((e) {
  //     controller.addError('Request error: $e');
  //     controller.close();
  //   });

  //   return controller.stream;
  // }

  // Stream<List> getUsersStream(message) async* {
  //   final headers = {
  //     'Accept': 'application/octet-stream',
  //     'Content-Type': 'application/json',
  //   };
  //   final body = jsonEncode({
  //     'message': message,
  //   });
  //   final request =
  //       http.Request('POST', Uri.parse('http://10.26.1.52:8445/chat'))
  //         ..headers.addAll(headers)
  //         ..body = body;

  //   final response = await http.Client().send(request);

  //   if (response.statusCode == 200) {
  //     await for (final chunk in response.stream
  //         .transform(utf8.decoder)
  //         .transform(const LineSplitter())) {
  //       if (chunk.isNotEmpty) {
  //         try {
  //           final String jsonStr =
  //               chunk.startsWith('data: ') ? chunk.substring(6) : chunk;
  //           final List<dynamic> usersJson = json.decode(jsonStr);
  //           // final List<UserModel> users = usersJson.map((json) => UserModel.fromJson(json)).toList();
  //           yield usersJson;
  //         } catch (e) {
  //           print('Error parsing chunk: $e');
  //           continue;
  //         }
  //       }
  //     }
  //   } else {
  //     throw Exception('Failed to connect to stream: ${response.statusCode}');
  //   }
  // }
}
